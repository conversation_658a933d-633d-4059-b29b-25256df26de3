{"name": "mystique", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@marsidev/react-turnstile": "^1.1.0", "better-auth": "^1.3.4", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "next": "15.4.5", "next-themes": "^0.4.6", "pg": "^8.16.3", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.15.5", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}